jQuery(document).ready(function($) {
    const chatButton = $('.wsl-chat-button');
    const chatWindow = $('.wsl-chat-window');
    
    chatButton.click(function() {
        chatWindow.slideToggle();
    });

    $('#wsl-chat-form').submit(function(e) {
        e.preventDefault();
        const message = $('#wsl-chat-input').val();
        
        $.ajax({
            url: wsl_chat.ajax_url,
            type: 'POST',
            data: {
                action: 'wsl_chat_message',
                nonce: wsl_chat.nonce,
                message: message
            },
            success: function(response) {
                if (response.success) {
                    appendMessage('bot', response.data);
                }
            }
        });
        
        $('#wsl-chat-input').val('');
    });
});