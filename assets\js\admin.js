jQuery(document).ready(function($) {
    // Initialize tabs
    $('.nav-tab-wrapper a').click(function(e) {
        e.preventDefault();
        $(this).addClass('nav-tab-active').siblings().removeClass('nav-tab-active');
        $($(this).attr('href')).addClass('active').siblings().removeClass('active');
    });

    // Initialize color picker
    $('.wsl-color-picker').wpColorPicker();

    // Handle training form submission
    $('#manual-training-form').submit(function(e) {
        e.preventDefault();
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wsl_save_training_data',
                nonce: wsl_admin.nonce,
                question: $('#question').val(),
                response: $('#response').val()
            },
            success: function(response) {
                if (response.success) {
                    alert('Training data saved successfully!');
                    $('#manual-training-form')[0].reset();
                }
            }
        });
    });
});