<?php
class WSL_Training {
    public function __construct() {
        add_action('init', array($this, 'init'));
    }

    public function init() {
        // Initialize training functionality
    }

    public function process_training_data($data) {
        // Process and store training data
        global $wpdb;
        $table_name = $wpdb->prefix . 'wsl_chatbot_training';
        
        return $wpdb->insert($table_name, array(
            'question' => $data['question'],
            'response' => $data['response'],
            'category' => $data['category'],
            'created_at' => current_time('mysql')
        ));
    }
}