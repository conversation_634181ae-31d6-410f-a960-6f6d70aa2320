<?php
class WSL_Chat_Handler {
    public function __construct() {
        add_action('wp_ajax_wsl_chat_message', array($this, 'handle_message'));
        add_action('wp_ajax_nopriv_wsl_chat_message', array($this, 'handle_message'));
    }

    public function handle_message() {
        check_ajax_referer('wsl_chat_nonce', 'nonce');
        
        $message = sanitize_text_field($_POST['message']);
        // Process message and generate response
        $response = $this->generate_response($message);
        
        wp_send_json_success($response);
    }

    private function generate_response($message) {
        // Generate response based on training data
        global $wpdb;
        $table_name = $wpdb->prefix . 'wsl_chatbot_training';
        
        // Simple matching for demo purposes
        $response = $wpdb->get_var($wpdb->prepare(
            "SELECT response FROM $table_name WHERE question LIKE %s LIMIT 1",
            '%' . $wpdb->esc_like($message) . '%'
        ));
        
        return $response ?: 'I\'m sorry, I don\'t understand that question.';
    }
}