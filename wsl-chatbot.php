<?php
/**
 * Plugin Name: WSL Custom Training ChatBot
 * Description: Advanced chatbot with custom training capabilities and comprehensive admin interface
 * Version: 1.0
 * Author: WSL Developer
 * Text Domain: wsl-chatbot
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WSL_Custom_ChatBot {
    private $plugin_name = 'WSL Custom Training ChatBot';
    private $version = '1.0.0';
    private $option_name = 'wsl_chatbot_settings';

    public function __construct() {
        $this->init_hooks();
    }

    private function init_hooks() {
        // Admin hooks
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Plugin functionality hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('wp_footer', array($this, 'render_chat_widget'));
        add_action('rest_api_init', array($this, 'register_rest_endpoints'));
        
        // Ajax handlers
        add_action('wp_ajax_wsl_save_training_data', array($this, 'handle_save_training_data'));
        add_action('wp_ajax_wsl_process_file_upload', array($this, 'handle_file_upload'));
    }

    public function enqueue_admin_assets($hook) {
        if (strpos($hook, 'wsl-chatbot') === false) {
            return;
        }

        wp_enqueue_style('wsl-chatbot-admin', plugins_url('assets/css/admin.css', __FILE__), array(), $this->version);
        wp_enqueue_script('wsl-chatbot-admin', plugins_url('assets/js/admin.js', __FILE__), array('jquery', 'jquery-ui-tabs'), $this->version);
        
        // Add color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
    }

    public function add_admin_menu() {
        add_menu_page(
            $this->plugin_name,
            'WSL ChatBot',
            'manage_options',
            'wsl-chatbot',
            array($this, 'render_main_page'),
            'dashicons-format-chat',
            30
        );

        // Add submenus
        add_submenu_page(
            'wsl-chatbot',
            __('Dashboard', 'wsl-chatbot'),
            __('Dashboard', 'wsl-chatbot'),
            'manage_options',
            'wsl-chatbot'
        );

        add_submenu_page(
            'wsl-chatbot',
            __('Training Data', 'wsl-chatbot'),
            __('Training Data', 'wsl-chatbot'),
            'manage_options',
            'wsl-chatbot-training',
            array($this, 'render_training_page')
        );

        add_submenu_page(
            'wsl-chatbot',
            __('Settings', 'wsl-chatbot'),
            __('Settings', 'wsl-chatbot'),
            'manage_options',
            'wsl-chatbot-settings',
            array($this, 'render_settings_page')
        );

        add_submenu_page(
            'wsl-chatbot',
            __('Analytics', 'wsl-chatbot'),
            __('Analytics', 'wsl-chatbot'),
            'manage_options',
            'wsl-chatbot-analytics',
            array($this, 'render_analytics_page')
        );
    }

    public function render_main_page() {
        ?>
        <div class="wrap wsl-chatbot-wrap">
            <h1><?php echo esc_html($this->plugin_name); ?> - Dashboard</h1>
            
            <div class="wsl-dashboard-grid">
                <!-- Quick Stats -->
                <div class="wsl-card">
                    <h2><?php _e('Quick Stats', 'wsl-chatbot'); ?></h2>
                    <?php $this->render_quick_stats(); ?>
                </div>

                <!-- Recent Interactions -->
                <div class="wsl-card">
                    <h2><?php _e('Recent Interactions', 'wsl-chatbot'); ?></h2>
                    <?php $this->render_recent_interactions(); ?>
                </div>

                <!-- Training Status -->
                <div class="wsl-card">
                    <h2><?php _e('Training Status', 'wsl-chatbot'); ?></h2>
                    <?php $this->render_training_status(); ?>
                </div>

                <!-- Quick Actions -->
                <div class="wsl-card">
                    <h2><?php _e('Quick Actions', 'wsl-chatbot'); ?></h2>
                    <?php $this->render_quick_actions(); ?>
                </div>
            </div>
        </div>
        <?php
    }

    public function render_training_page() {
        ?>
        <div class="wrap wsl-chatbot-wrap">
            <h1><?php _e('Training Management', 'wsl-chatbot'); ?></h1>

            <div class="nav-tab-wrapper">
                <a href="#file-upload" class="nav-tab nav-tab-active"><?php _e('File Upload', 'wsl-chatbot'); ?></a>
                <a href="#manual-training" class="nav-tab"><?php _e('Manual Training', 'wsl-chatbot'); ?></a>
                <a href="#training-data" class="nav-tab"><?php _e('Training Data', 'wsl-chatbot'); ?></a>
                <a href="#import-export" class="nav-tab"><?php _e('Import/Export', 'wsl-chatbot'); ?></a>
            </div>

            <div class="tab-content">
                <div id="file-upload" class="tab-pane active">
                    <div class="wsl-card">
                        <h2><?php _e('Upload Training Files', 'wsl-chatbot'); ?></h2>
                        <form method="post" enctype="multipart/form-data" class="wsl-upload-form">
                            <?php wp_nonce_field('wsl_file_upload', 'wsl_upload_nonce'); ?>
                            <div class="form-group">
                                <label for="training_file"><?php _e('Select File', 'wsl-chatbot'); ?></label>
                                <input type="file" name="training_file" id="training_file" accept=".csv,.xml,.pdf,.txt" required>
                                <p class="description"><?php _e('Supported formats: CSV, XML, PDF, TXT', 'wsl-chatbot'); ?></p>
                            </div>
                            <button type="submit" class="button button-primary"><?php _e('Upload and Process', 'wsl-chatbot'); ?></button>
                        </form>
                    </div>
                </div>

                <div id="manual-training" class="tab-pane">
                    <div class="wsl-card">
                        <h2><?php _e('Add Training Data Manually', 'wsl-chatbot'); ?></h2>
                        <form id="manual-training-form">
                            <div class="form-group">
                                <label for="question"><?php _e('Question', 'wsl-chatbot'); ?></label>
                                <input type="text" name="question" id="question" class="regular-text" required>
                            </div>
                            <div class="form-group">
                                <label for="response"><?php _e('Response', 'wsl-chatbot'); ?></label>
                                <textarea name="response" id="response" rows="4" class="large-text" required></textarea>
                            </div>
                            <button type="submit" class="button button-primary"><?php _e('Add Training Data', 'wsl-chatbot'); ?></button>
                        </form>
                    </div>
                </div>

                <div id="training-data" class="tab-pane">
                    <div class="wsl-card">
                        <h2><?php _e('Current Training Data', 'wsl-chatbot'); ?></h2>
                        <?php $this->render_training_data_table(); ?>
                    </div>
                </div>

                <div id="import-export" class="tab-pane">
                    <div class="wsl-card">
                        <h2><?php _e('Import/Export Training Data', 'wsl-chatbot'); ?></h2>
                        <div class="import-export-controls">
                            <div class="export-section">
                                <h3><?php _e('Export Data', 'wsl-chatbot'); ?></h3>
                                <button class="button button-secondary" id="export-csv"><?php _e('Export as CSV', 'wsl-chatbot'); ?></button>
                                <button class="button button-secondary" id="export-xml"><?php _e('Export as XML', 'wsl-chatbot'); ?></button>
                            </div>
                            <div class="import-section">
                                <h3><?php _e('Import Data', 'wsl-chatbot'); ?></h3>
                                <form method="post" enctype="multipart/form-data">
                                    <?php wp_nonce_field('wsl_import_data', 'wsl_import_nonce'); ?>
                                    <input type="file" name="import_file" accept=".csv,.xml" required>
                                    <button type="submit" class="button button-primary"><?php _e('Import', 'wsl-chatbot'); ?></button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    public function render_settings_page() {
        ?>
        <div class="wrap wsl-chatbot-wrap">
            <h1><?php _e('ChatBot Settings', 'wsl-chatbot'); ?></h1>

            <div class="nav-tab-wrapper">
                <a href="#general" class="nav-tab nav-tab-active"><?php _e('General', 'wsl-chatbot'); ?></a>
                <a href="#appearance" class="nav-tab"><?php _e('Appearance', 'wsl-chatbot'); ?></a>
                <a href="#behavior" class="nav-tab"><?php _e('Behavior', 'wsl-chatbot'); ?></a>
                <a href="#advanced" class="nav-tab"><?php _e('Advanced', 'wsl-chatbot'); ?></a>
            </div>

            <form method="post" action="options.php">
                <?php settings_fields('wsl_chatbot_settings'); ?>
                
                <div class="tab-content">
                    <div id="general" class="tab-pane active">
                        <div class="wsl-card">
                            <h2><?php _e('General Settings', 'wsl-chatbot'); ?></h2>
                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Enable ChatBot', 'wsl-chatbot'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="<?php echo $this->option_name; ?>[enabled]" value="1" <?php checked(get_option($this->option_name)['enabled'] ?? false); ?>>
                                            <?php _e('Enable ChatBot on website', 'wsl-chatbot'); ?>
                                        </label>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('ChatBot Name', 'wsl-chatbot'); ?></th>
                                    <td>
                                        <input type="text" name="<?php echo $this->option_name; ?>[name]" value="<?php echo esc_attr(get_option($this->option_name)['name'] ?? 'WSL Assistant'); ?>" class="regular-text">
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('Welcome Message', 'wsl-chatbot'); ?></th>
                                    <td>
                                        <textarea name="<?php echo $this->option_name; ?>[welcome_message]" rows="3" class="large-text"><?php echo esc_textarea(get_option($this->option_name)['welcome_message'] ?? 'How can I help you today?'); ?></textarea>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div id="appearance" class="tab-pane">
                        <div class="wsl-card">
                            <h2><?php _e('Appearance Settings', 'wsl-chatbot'); ?></h2>
                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Theme Color', 'wsl-chatbot'); ?></th>
                                    <td>
                                        <input type="text" name="<?php echo $this->option_name; ?>[theme_color]" value="<?php echo esc_attr(get_option($this->option_name)['theme_color'] ?? '#0073aa'); ?>" class="wsl-color-picker">
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('Chat Window Position', 'wsl-chatbot'); ?></th>
                                    <td>
                                        <select name="<?php echo $this->option_name; ?>[position]">
                                            <option value="right" <?php selected(get_option($this->option_name)['position'] ?? 'right', 'right'); ?>><?php _e('Right', 'wsl-chatbot'); ?></option>
                                            <option value="left" <?php selected(get_option($this->option_name)['position'] ?? 'right', 'left'); ?>><?php _e('Left', 'wsl-chatbot'); ?></option>
                                        </select>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div id="behavior" class="tab-pane">
                        <div class="wsl-card">
                            <h2><?php _e('Behavior Settings', 'wsl-chatbot'); ?></h2>
                            <table class="form-table">
                                <tr>
                                    <th scope="row"><?php _e('Auto Open', 'wsl-chatbot'); ?></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="<?php echo $this->option_name; ?>[auto_open]" value="1" <?php checked(get_option($this->option_name)['auto_open'] ?? false); ?>>
                                            <?php _e('Automatically open chat window after page load', 'wsl-chatbot'); ?>
                                        </label>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row"><?php _e('Auto Open Delay (seconds)', 'wsl-chatbot